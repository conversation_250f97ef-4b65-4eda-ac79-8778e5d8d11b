import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create a Supabase client with service role for server-side operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

interface FilterOption {
  value: string;
  label: string;
  count: number;
}

interface FilterOptionsResponse {
  platforms: FilterOption[];
  merchants: FilterOption[];
  statuses: FilterOption[];
  networks: FilterOption[];
  transactionTypes: FilterOption[];
  error?: string;
}

// Helper function to get filter counts using database function (super efficient!)
async function getFilterCounts(filterType: string, limit?: number, platform?: string): Promise<FilterOption[]> {
  try {
    console.log(`Fetching filter counts for ${filterType}${platform ? ` (platform: ${platform})` : ''} using database function...`);

    // If platform is specified, we need to query the table directly with platform filter
    if (platform) {
      // Map filter types to actual column names
      const columnMap: { [key: string]: string } = {
        'platform': 'platform',
        'merchant_name': 'merchant_name',
        'status': 'status',
        'network_name': 'network_name',
        'transaction_type': 'transaction_type'
      };

      const column = columnMap[filterType];
      if (!column) {
        console.error(`Unknown filter type: ${filterType}`);
        return [];
      }

      // Query with platform filter - handle pagination to get ALL data
      let allData: any[] = [];
      let hasMore = true;
      let offset = 0;
      const batchSize = 1000;

      while (hasMore) {
        const { data: batchData, error } = await supabaseAdmin
          .from('normalized_transactions')
          .select(column)
          .eq('platform', platform)
          .not(column, 'is', null)
          .range(offset, offset + batchSize - 1);

        if (error) {
          console.error(`Error querying ${filterType} with platform filter:`, error);
          return [];
        }

        if (batchData && batchData.length > 0) {
          allData = allData.concat(batchData);
          offset += batchSize;
          hasMore = batchData.length === batchSize; // Continue if we got a full batch
        } else {
          hasMore = false;
        }

        // Safety check to prevent infinite loops
        if (offset > 10000000) { // 10M rows safety limit
          console.warn(`[FilterOptions] Safety limit reached for ${filterType}, stopping pagination`);
          break;
        }
      }

      console.log(`[FilterOptions] Fetched ${allData.length} rows for ${filterType} on ${platform}`);

      // Count occurrences
      const counts: { [key: string]: number } = {};
      allData.forEach((item: any) => {
        const value = item[column];
        if (value) {
          counts[value] = (counts[value] || 0) + 1;
        }
      });

      const result = Object.entries(counts)
        .map(([value, count]) => ({
          value,
          label: formatLabel(filterType, value),
          count
        }))
        .sort((a: FilterOption, b: FilterOption) => b.count - a.count);

      console.log(`${filterType} results for ${platform}:`, result);
      return result;
    } else {
      // Use our custom database function for maximum efficiency (original behavior)
      const { data, error } = await supabaseAdmin
        .rpc('get_filter_options')
        .eq('filter_type', filterType);

      if (error) {
        console.error(`Error calling database function for ${filterType}:`, error);
        return [];
      }

      const result = (data || [])
        .map((item: any) => ({
          value: item.value,
          label: formatLabel(filterType, item.value),
          count: parseInt(item.count)
        }))
        .sort((a: FilterOption, b: FilterOption) => b.count - a.count);

      console.log(`${filterType} results from DB function:`, result);
      return result;
    }
  } catch (error) {
    console.error(`Error getting filter counts for ${filterType}:`, error);
    return [];
  }
}

// Helper function to format labels
function formatLabel(column: string, value: string): string {
  switch (column) {
    case 'platform':
      return value === 'strackr' ? 'Strackr' : value.charAt(0).toUpperCase() + value.slice(1);
    case 'status':
    case 'transaction_type':
      return value.charAt(0).toUpperCase() + value.slice(1);
    default:
      return value;
  }
}

export async function GET(request: NextRequest) {
  try {
    // Don't cache while we're debugging
    const headers = {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    };

    const { searchParams } = new URL(request.url);
    const platform = searchParams.get('platform');

    console.log(`Fetching filter options${platform ? ` for platform: ${platform}` : ''} with improved batching method...`);

    // Use our efficient database function to get all filter counts in parallel
    // If platform is specified, filter the options for that platform only
    const [platforms, merchants, statuses, networks, transactionTypes] = await Promise.all([
      platform ? [] : getFilterCounts('platform'), // Don't fetch platforms if we're filtering by platform
      getFilterCounts('merchant_name', undefined, platform), // Remove limit to show all merchants
      getFilterCounts('status', undefined, platform),
      getFilterCounts('network_name', undefined, platform),
      getFilterCounts('transaction_type', undefined, platform)
    ]);

    console.log('Filter options fetched successfully:', {
      platforms: platforms.length,
      merchants: merchants.length,
      statuses: statuses.length,
      networks: networks.length,
      transactionTypes: transactionTypes.length
    });

    // Log the actual transaction types for debugging
    console.log('Transaction types found:', transactionTypes);

    const response: FilterOptionsResponse = {
      platforms,
      merchants,
      statuses,
      networks,
      transactionTypes
    };

    return NextResponse.json(response, { headers });

  } catch (error) {
    console.error('Filter options API route error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
